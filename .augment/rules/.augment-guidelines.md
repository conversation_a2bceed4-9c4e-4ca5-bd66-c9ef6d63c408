---
type: 'always_apply'
---

# Augment Guidelines for Enhanced Development Experience

## Project Context

project:
name: "Store Hub App"
description: "Modern React/TypeScript application with mobile-first design"
type: "web-application"
stack: ["React", "TypeScript", "Tailwind CSS", "Next.js"]

## Core Development Philosophy

### 1. Simplicity First

- Prioritize simple, clear, and maintainable solutions
- Avoid unnecessary complexity or over-engineering
- Prefer iterating on existing code rather than complete rewrites
- Focus on the specific task assigned without scope creep

### 2. Code Quality Standards

- Use strict TypeScript typing (avoid `any` types)
- Keep files under 300 lines - refactor proactively
- Write clean, well-organized, and readable code
- Follow established project patterns consistently
- Use descriptive variable and function names

### 3. Communication Patterns

- ALWAYS explain your approach in plain language BEFORE writing code
- Ask clarifying questions when requirements are unclear
- Propose solutions and get approval before implementation
- If no questions needed, explicitly state "No questions - proceeding with implementation"
- Break down complex changes into smaller, reviewable chunks

## Technical Guidelines

### TypeScript & React

- Use Composition API with TypeScript for Vue components
- Include .vue/.tsx extensions when importing components
- Prefer functional components with hooks over class components
- Use proper TypeScript interfaces for props and state
- Document complex logic with JSDoc comments

### Styling & UI

- Use Tailwind CSS for styling (avoid custom CSS when possible)
- Follow mobile-first responsive design principles
- Ensure accessibility standards (ARIA labels, semantic HTML)
- Maintain consistent spacing and typography scales
- Test on mobile (360px) and tablet breakpoints

### Code Organization

- Group related functionality together
- Use clear directory structure following project conventions
- Separate concerns (UI, logic, data, utilities)
- Create reusable components for common patterns
- Keep business logic separate from UI components

### Error Handling & Testing

- Implement proper error boundaries in React
- Use try-catch blocks for async operations
- Write meaningful error messages for users
- Include unit tests for critical functionality
- Test edge cases and error scenarios

## Workflow Guidelines

### Development Process

1. **Understand Requirements**: Read and clarify the task thoroughly
2. **Plan Approach**: Explain the solution strategy in plain language
3. **Get Approval**: Wait for confirmation before coding
4. **Implement Incrementally**: Make small, focused changes
5. **Test & Verify**: Ensure changes work as expected
6. **Document**: Update relevant documentation

### Git & Version Control

- Write clear, descriptive commit messages
- Make atomic commits (one logical change per commit)
- Keep working directory clean
- Follow established branching strategy
- Don't commit sensitive information or build artifacts

### Package Management

- Use npm/yarn for dependency management
- Don't manually edit package.json for dependencies
- Use exact versions for critical dependencies
- Keep dependencies up to date and secure
- Document any special installation requirements

## Communication Rules

### Response Format

- Start with understanding confirmation: "I understand you want to [task summary]"
- Explain approach: "My plan is to [solution strategy]"
- Ask questions: "Before proceeding, I need to clarify [specific questions]"
- Wait for approval: "Should I proceed with this approach?"
- Only then provide code implementation

### Code Suggestions

- Provide context for why changes are needed
- Explain any trade-offs or alternatives considered
- Highlight any breaking changes or dependencies
- Include testing recommendations
- Suggest follow-up improvements if relevant

### Prohibited Behaviors

- NEVER provide code without explaining the approach first
- NEVER make unrelated changes when fixing specific issues
- NEVER use overly defensive programming without justification
- NEVER ignore established project patterns
- NEVER commit or deploy code without explicit permission

## Project-Specific Patterns

### File Structure

- Components in `/components` with clear naming
- Utilities in `/utils` or `/lib`
- Types in `/types` or co-located with components
- Tests alongside source files or in `__tests__`
- Documentation in `/docs` directory

### Naming Conventions

- Use PascalCase for components and interfaces
- Use camelCase for functions and variables
- Use kebab-case for file names
- Use UPPER_CASE for constants
- Prefix boolean variables with is/has/can/should

### Performance Considerations

- Optimize for mobile performance first
- Use lazy loading for large components
- Implement proper caching strategies
- Minimize bundle size and dependencies
- Monitor and measure performance impacts

## Integration Guidelines

### External Services

- Use environment variables for configuration
- Implement proper error handling for API calls
- Add retry logic for network requests
- Cache responses when appropriate
- Log important events and errors

### Security Practices

- Validate all user inputs
- Sanitize data before display
- Use HTTPS for all external requests
- Store secrets securely (never in code)
- Implement proper authentication/authorization

## Quality Assurance

### Before Submitting Code

1. Code compiles without errors or warnings
2. All tests pass
3. Code follows project style guidelines
4. Documentation is updated if needed
5. No sensitive information is included
6. Performance impact is acceptable

### Review Checklist

- Functionality works as specified
- Code is readable and maintainable
- Error handling is appropriate
- Security considerations are addressed
- Performance is acceptable
- Tests cover critical paths

Remember: These guidelines ensure consistent, high-quality development. When in doubt, ask for clarification rather than making assumptions.
